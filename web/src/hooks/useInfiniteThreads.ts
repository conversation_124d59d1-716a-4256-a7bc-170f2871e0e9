import useSWRInfinite from "swr/infinite";
import { Thread } from "@langchain/langgraph-sdk";
import { createClient, getDefaultApiUrl } from "@/providers/client";
import { THREAD_SWR_CONFIG, DEFAULT_PAGINATION } from "@/lib/swr-config";
import { 
  AnyGraphState, 
  PaginationOptions, 
  ThreadSortBy, 
  SortOrder 
} from "@/types/thread";
import { useMemo, useCallback } from "react";

/**
 * 无限滚动线程选项接口
 */
interface UseInfiniteThreadsOptions {
  /**
   * 助手ID（图ID），用于过滤特定图的线程
   */
  assistantId?: string;
  /**
   * 刷新间隔（毫秒）
   */
  refreshInterval?: number;
  /**
   * 窗口聚焦时重新验证
   */
  revalidateOnFocus?: boolean;
  /**
   * 重新连接时重新验证
   */
  revalidateOnReconnect?: boolean;
  /**
   * 当前用户ID，用于过滤用户的线程
   */
  currentUserId?: string | null;
  /**
   * 禁用用户过滤
   */
  disableUserFiltering?: boolean;
  /**
   * 每页大小
   */
  pageSize?: number;
  /**
   * 排序字段
   */
  sortBy?: ThreadSortBy;
  /**
   * 排序顺序
   */
  sortOrder?: SortOrder;
}

/**
 * 线程搜索参数
 */
interface ThreadSearchArgs {
  metadata?: {
    graph_id?: string;
    user_id?: string;
  };
  limit?: number;
  offset?: number;
  sortBy?: ThreadSortBy;
  sortOrder?: SortOrder;
}

/**
 * 无限滚动线程Hook
 * 支持分页加载和无限滚动功能
 * 
 * @param options 配置选项
 * @returns 线程数据和相关状态
 */
export function useInfiniteThreads<
  TGraphState extends AnyGraphState = AnyGraphState,
>(options: UseInfiniteThreadsOptions = {}) {
  const {
    assistantId,
    refreshInterval = THREAD_SWR_CONFIG.refreshInterval,
    revalidateOnFocus = THREAD_SWR_CONFIG.revalidateOnFocus,
    revalidateOnReconnect = THREAD_SWR_CONFIG.revalidateOnReconnect,
    currentUserId,
    disableUserFiltering = false,
    pageSize = DEFAULT_PAGINATION.limit,
    sortBy = DEFAULT_PAGINATION.sortBy,
    sortOrder = DEFAULT_PAGINATION.sortOrder,
  } = options;

  const apiUrl = getDefaultApiUrl();

  // 生成SWR键的函数
  const getKey = useCallback(
    (pageIndex: number, previousPageData: Thread<TGraphState>[] | null) => {
      // 如果上一页没有数据，说明已经到达末尾
      if (previousPageData && !previousPageData.length) return null;

      const baseKey = assistantId ? ["threads", assistantId] : ["threads", "all"];
      return [
        ...baseKey,
        "infinite",
        pageIndex,
        pageSize,
        sortBy,
        sortOrder,
        currentUserId || "no-user",
      ];
    },
    [assistantId, pageSize, sortBy, sortOrder, currentUserId]
  );

  // 数据获取器
  const fetcher = useCallback(
    async (key: any[]): Promise<Thread<TGraphState>[]> => {
      if (!apiUrl) {
        throw new Error("API URL is not configured");
      }

      const [, , , , pageIndex] = key;
      const offset = pageIndex * pageSize;

      const client = createClient(apiUrl);
      
      // 构建搜索参数
      const searchArgs: ThreadSearchArgs = {
        limit: pageSize,
        offset,
        sortBy,
        sortOrder,
      };

      // 添加元数据过滤
      if (assistantId || currentUserId) {
        searchArgs.metadata = {};
        if (assistantId) {
          searchArgs.metadata.graph_id = assistantId;
        }
        if (currentUserId && !disableUserFiltering) {
          searchArgs.metadata.user_id = currentUserId;
        }
      }

      try {
        return await client.threads.search<TGraphState>(searchArgs);
      } catch (error) {
        console.error("Failed to fetch threads:", error);
        throw error;
      }
    },
    [apiUrl, pageSize, sortBy, sortOrder, assistantId, currentUserId, disableUserFiltering]
  );

  // 使用SWR Infinite进行数据获取
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    size,
    setSize,
  } = useSWRInfinite(getKey, fetcher, {
    refreshInterval,
    revalidateOnFocus,
    revalidateOnReconnect,
    errorRetryCount: THREAD_SWR_CONFIG.errorRetryCount,
    errorRetryInterval: THREAD_SWR_CONFIG.errorRetryInterval,
    dedupingInterval: THREAD_SWR_CONFIG.dedupingInterval,
  });

  // 扁平化所有页面的线程数据
  const threads = useMemo(() => {
    if (!data) return [];
    
    const allThreads = data.flat();
    
    if (disableUserFiltering) {
      return allThreads;
    }

    if (!currentUserId) {
      return allThreads;
    }

    // 按用户ID过滤线程
    return allThreads.filter((thread) => {
      const threadUserId = thread.metadata?.user_id;
      return (
        typeof threadUserId === "string" &&
        threadUserId === currentUserId
      );
    });
  }, [data, currentUserId, disableUserFiltering]);

  // 检查是否还有更多数据
  const hasMore = useMemo(() => {
    if (!data) return false;
    const lastPage = data[data.length - 1];
    return lastPage && lastPage.length === pageSize;
  }, [data, pageSize]);

  // 加载下一页
  const loadMore = useCallback(() => {
    if (hasMore && !isLoading && !isValidating) {
      setSize(size + 1);
    }
  }, [hasMore, isLoading, isValidating, setSize, size]);

  // 刷新所有数据
  const refresh = useCallback(() => {
    mutate();
  }, [mutate]);

  // 重置到第一页
  const reset = useCallback(() => {
    setSize(1);
  }, [setSize]);

  return {
    threads,
    error,
    isLoading,
    isValidating,
    hasMore,
    loadMore,
    refresh,
    reset,
    mutate,
    size,
  };
}
